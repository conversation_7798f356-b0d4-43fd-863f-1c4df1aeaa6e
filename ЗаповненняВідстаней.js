/**
 * @OnlyCurrentDoc
 */

function fillDistancesFromRoutes() {
  const ui = SpreadsheetApp.getUi();
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const activeSheet = spreadsheet.getActiveSheet();
  const activeRange = activeSheet.getActiveRange();
  const activeRowIndex = activeRange.getRow();

  // 1. Перевірка, чи вибрано рівно один рядок
  if (activeRange.getNumRows() !== 1) {
    ui.alert('Будь ласка, виберіть один рядок для обробки.');
    return;
  }

  // Отримуємо всі значення з активного рядка, незалежно від вибраної клітинки
  const rowValues = activeSheet.getRange(activeRowIndex, 1, 1, activeSheet.getLastColumn()).getValues()[0];
  const headers = activeSheet.getRange(1, 1, 1, activeSheet.getLastColumn()).getValues()[0].map(h => String(h).trim());

  // Допоміжна функція для пошуку індексу стовпця за назвою (регістронезалежно)
  function findColumnIndex(headerNames, targetColumnName) {
    for (let i = 0; i < headerNames.length; i++) {
      if (headerNames[i].toLowerCase() === targetColumnName.toLowerCase()) {
        return i;
      }
    }
    return -1;
  }

  // 2. Доступ до аркуша "Маршрути 3"
  const routesSheet = spreadsheet.getSheetByName("Маршрути 3");
  if (!routesSheet) {
    ui.alert('Аркуш "Маршрути 3" не знайдено. Будь ласка, створіть його.');
    return;
  }

  // 3. Отримання даних та заголовків з аркуша "Маршрути 3"
  const routesData = routesSheet.getDataRange().getValues();
  const routesHeaders = routesData.length > 0 ? routesData[0].map(h => String(h).trim()) : [];

  const fromColIndexRoutes = findColumnIndex(routesHeaders, 'Від');
  const toColIndexRoutes = findColumnIndex(routesHeaders, 'До');
  const distanceKmColIndexRoutes = findColumnIndex(routesHeaders, 'Відстань км.');

  if (fromColIndexRoutes === -1 || toColIndexRoutes === -1 || distanceKmColIndexRoutes === -1) {
    ui.alert('Не вдалося знайти всі необхідні стовпці в аркуші "Маршрути 3": "Від", "До", "Відстань км.". Перевірте назви.');
    return;
  }

  let results = [];

  // Допоміжна функція для пошуку та заповнення даних маршруту
  function lookupAndFillRoute(sourceCityColName, destCityColName, targetDistanceColName) {
    const sourceCityColIndex = findColumnIndex(headers, sourceCityColName);
    const destCityColIndex = findColumnIndex(headers, destCityColName);
    const targetDistanceColIndex = findColumnIndex(headers, targetDistanceColName);

    if (sourceCityColIndex === -1 || destCityColIndex === -1 || targetDistanceColIndex === -1) {
      return `Не знайдено всі необхідні стовпці в активному аркуші для маршруту ${sourceCityColName} - ${destCityColName}.`;
    }

    const sourceCity = String(rowValues[sourceCityColIndex] || '').trim();
    const destinationCity = String(rowValues[destCityColIndex] || '').trim();

    if (!sourceCity || !destinationCity) {
      return `Порожні значення міст для маршруту ${sourceCityColName} - ${destCityColName}.`;
    }

    let foundMatch = false;
    for (let i = 1; i < routesData.length; i++) {
      const routeRow = routesData[i];
      if (String(routeRow[fromColIndexRoutes] || '').trim() === sourceCity && String(routeRow[toColIndexRoutes] || '').trim() === destinationCity) {
        const foundDistance = String(routeRow[distanceKmColIndexRoutes] || '').trim();

        // Заповнюємо відстань
        activeSheet.getRange(activeRowIndex, targetDistanceColIndex + 1).setValue(foundDistance);
        results.push(`${sourceCity} - ${destinationCity}: Відстань ${foundDistance} км. записано в '${targetDistanceColName}'.`);
        
        foundMatch = true;
        break;
      }
    }

    if (!foundMatch) {
      return `Маршрут ${sourceCity} - ${destinationCity} не знайдено в аркуші "Маршрути 3".`;
    }
    return null; // Повертаємо null, якщо успішно знайдено та заповнено
  }

  // Обробка маршрутів
  results.push(lookupAndFillRoute('Подача', 'Завантаження', 'Подача км.'));
  results.push(lookupAndFillRoute('Завантаження', 'Кордон', 'До кордону км.'));
  results.push(lookupAndFillRoute('Кордон', 'Розвантаження', 'Після кордону км.'));
  results.push(lookupAndFillRoute('Розвантаження', 'Повернення', 'Повернення км.'));

  // Фільтруємо null-результати та виводимо повідомлення
  const finalMessages = results.filter(msg => msg !== null);
  if (finalMessages.length > 0) {
    ui.alert('Результати заповнення відстаней:\n\n' + finalMessages.join('\n'));
  } else {
    ui.alert('Усі маршрути успішно заповнено.');
  }
}
