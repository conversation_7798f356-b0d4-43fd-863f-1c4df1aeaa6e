## Progress Update

**Дата:** 21.06.2025

### Виконані завдання:
*   Ініціалізовано Git репозиторій у папці `c:/programing/StellarAppScript`.
*   Створено файл `.gitignore` для ігнорування `node_modules/` та `.clasp.json`.
*   Підключено локальний репозиторій до віддаленого GitHub репозиторію: `https://github.com/MishaKrutiy/StellarAppScript.git`.
*   Встановлено Node.js LTS (виправлено проблему з правами адміністратора).
*   Встановлено `@google/clasp` глобально.
*   Виконано вхід у `clasp` (авторизація через Google).
*   Клоновано проект Google Apps Script з ID `"1zz-RUUIE853s7jXBYqlsinlGpnem6_yLLPcP1vUdhxsgpTjDf6GLLC9T"` у локальну папку.
*   Зафіксовано всі завантажені файли у Git репозиторії.
*   Відправлено (pushed) зміни на GitHub.
*   Витягнуто новий код App Script за допомогою `clasp pull`.
*   Оновлено `GeoNames API.js`: додано функцію `getCityCoordinates` для отримання географічних координат міст.
*   Оновлено `TollCalculator.js`:
    - Додано тестову функцію `testCalculateTollCost`.
    - Виправлено помилку `ReferenceError: getCityCoordinates is not defined` шляхом успішного `clasp push` після ввімкнення Apps Script API.
    - Виправлено помилку `Invalid value for parameter 'truck[width]'` шляхом зміни `width` на `250` (сантиметри).
    - Виправлено помилку `Spans requested but no polyline requested` шляхом переходу на POST-запит та передачі всіх параметрів вантажівки в JSON-тілі запиту.
    - Забезпечено успішне виконання запиту до HERE Routing API (HTTP 200 OK).
    - Виправлено помилку "unknown field `origin`" шляхом переміщення параметрів `origin` та `destination` з тіла POST-запиту до URL запиту.
    - **Виправлено помилку "unknown field `truck`" шляхом переміщення всіх параметрів транспортного засобу (`truck`) з тіла POST-запиту до URL запиту та зміни методу запиту на GET.**

### Поточний статус:
Проект успішно налаштований, синхронізований з GitHub та Google Apps Script. Всі файли з Apps Script завантажені та зафіксовані в репозиторії. Локальний код синхронізовано з віддаленим. Функціонал для отримання координат міст та розрахунку вартості платних доріг (з успішним запитом до HERE API) реалізовані.

### Наступні кроки:
Основне завдання "завантаж код з папки в clasp" виконано. Подальше налагодження логіки розрахунку вартості платних доріг (чому повертається 0 євро) вимагатиме додаткового аналізу відповіді HERE API.
