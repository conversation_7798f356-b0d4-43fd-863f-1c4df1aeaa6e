function GET_DISTANCE(cityFrom, cityTo) {
  const apiKey = 'AIzaSyAihLb-pXhvGV6iaXTbtBvyaE6pcNqIu2Q';
  const baseUrl = 'https://maps.googleapis.com/maps/api/distancematrix/json';
  const url = `${baseUrl}?origins=${encodeURIComponent(cityFrom)}&destinations=${encodeURIComponent(cityTo)}&key=${apiKey}&language=uk`;

  Logger.log('URL: ' + url);

  try {
    const response = UrlFetchApp.fetch(url);
    const data = JSON.parse(response.getContentText());

    const status = data.rows[0].elements[0].status;
    Logger.log('Status: ' + status);
    Logger.log(JSON.stringify(data));

    if (status === 'OK') {
      const meters = data.rows[0].elements[0].distance.value;
      return (meters / 1000).toFixed(1) * 1;
    } else {
      return 'Помилка: ' + status;
    }
  } catch (e) {
    Logger.log('Помилка запиту: ' + e);
    return 'Помилка запиту';
  }
}

function TEST_GET_DISTANCE() {
  const cityFrom = "Львів, Україна";
  const cityTo = "Берлін, Німеччина";

  Logger.log(`Тестуємо маршрут: ${cityFrom} → ${cityTo}`);

  const distance = GET_DISTANCE(cityFrom, cityTo);

  Logger.log(`Результат: ${distance} км`);
}

