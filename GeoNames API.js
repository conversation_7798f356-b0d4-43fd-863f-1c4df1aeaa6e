function importEuropeCitiesAll() {
  const countries = ['AL','AD','AT','BY','BE','BA','BG','HR','CY','CZ','DK','EE','FI','FR','DE','GR','HU','IS','IE','IT','KZ','LV','LI','LT','LU','MT','MD','MC','ME','NL','MK','NO','PL','PT','RO','RU','SM','RS','SK','SI','ES','SE','CH','UA','TR'];
  const username = 'mishakrutiy';
  const maxRows = 1000;

  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let sheet = ss.getSheetByName("EuropeCitiesFull");
  if (!sheet) {
    sheet = ss.insertSheet("EuropeCitiesFull");
  } else {
    sheet.clearContents();
  }

  sheet.appendRow(['City (ascii)', 'City (native)', 'Admin1', 'Country', 'ISO2', 'Lat', 'Lng', 'Population']);

  countries.forEach(code => {
    let startRow = 0;
    let totalCount = 0;

    while (true) {
      const url = `http://api.geonames.org/searchJSON?country=${code}&featureClass=P&maxRows=${maxRows}&startRow=${startRow}&username=${username}`;
      try {
        const response = UrlFetchApp.fetch(url);
        const data = JSON.parse(response.getContentText());

        if (!data.geonames || data.geonames.length === 0) {
          Logger.log(`🔚 [${code}] Завершено (startRow=${startRow})`);
          break;
        }

        const rows = data.geonames.map(c => [
          c.asciiName || c.name || '',
          c.name || '',
          c.adminName1 || '',
          c.countryName || '',
          c.countryCode || '',
          c.lat || '',
          c.lng || '',
          c.population || ''
        ]);

        sheet.getRange(sheet.getLastRow() + 1, 1, rows.length, 8).setValues(rows);
        totalCount += rows.length;
        Logger.log(`✅ [${code}] Імпортовано ${rows.length} (загалом: ${totalCount})`);

        startRow += maxRows;
        Utilities.sleep(1000);
      } catch (e) {
        Logger.log(`❌ [${code}] Помилка: ${e}`);
        break;
      }
    }
  });

  Logger.log("🎉 Імпорт завершено повністю.");
}

/**
 * Отримує географічні координати міста за його назвою.
 * @param {string} cityName Назва міста.
 * @returns {object|null} Об'єкт з широтою (lat) та довготою (lng) або null, якщо місто не знайдено.
 */
function getCityCoordinates(cityName) {
  const username = 'mishakrutiy'; // Використовуємо ваш username

  // Додаємо більш специфічні параметри для пошуку великих міст
  const url = `http://api.geonames.org/searchJSON?q=${encodeURIComponent(cityName)}&maxRows=5&featureClass=P&orderby=population&username=${username}`;

  Logger.log(`🔍 Запит до GeoNames API для ${cityName}: ${url}`);

  try {
    const response = UrlFetchApp.fetch(url);
    const data = JSON.parse(response.getContentText());

    Logger.log(`📋 Відповідь GeoNames API для ${cityName}:`, JSON.stringify(data, null, 2));

    if (data.geonames && data.geonames.length > 0) {
      // Вибираємо найбільше місто за населенням
      const city = data.geonames[0];
      Logger.log(`✅ Знайдено координати для ${cityName}: ${city.name} (${city.countryName}) - Lat ${city.lat}, Lng ${city.lng}, Population: ${city.population}`);
      return { lat: parseFloat(city.lat), lng: parseFloat(city.lng) };
    } else {
      Logger.log(`❌ Місто ${cityName} не знайдено або немає координат.`);
      return null;
    }
  } catch (e) {
    Logger.log(`❌ Помилка при отриманні координат для ${cityName}: ${e.message}`);
    return null;
  }
}
