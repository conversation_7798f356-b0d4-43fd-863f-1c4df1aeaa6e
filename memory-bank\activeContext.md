# Active Context

## Current Work Focus
Поточне завдання полягає в налагодженні функціоналу розрахунку вартості платних доріг для фур між містами за допомогою HERE Routing API.

## Recent Changes
- Створено `projectbrief.md` з оглядом проекту, цілями та обсягом.
- Створено `productContext.md` з описом призначення, вирішених проблем, очікуваної роботи та цілей користувацького досвіду.
- Створено `systemPatterns.md` з інформацією про архітектуру системи, ключові технічні рішення, шаблони проектування та взаємозв'язки компонентів.
- Створено `techContext.md` з деталями про використовувані технології, налаштування розробки, технічні обмеження, залежності та шаблони використання інструментів.
- Оновлено `GeoNames API.js`: додано функцію `getCityCoordinates` для отримання географічних координат міст.
- Оновлено `TollCalculator.js`:
    - Додано тестову функцію `testCalculateTollCost`.
    - Виправлено помилку `ReferenceError: getCityCoordinates is not defined` шляхом успішного `clasp push` після ввімкнення Apps Script API.
    - Виправлено помилку `Invalid value for parameter 'truck[width]'` шляхом зміни `width` на `250` (сантиметри).
    - Виправлено помилку `Spans requested but no polyline requested` шляхом переходу на POST-запит та передачі всіх параметрів вантажівки в JSON-тілі запиту.
    - Забезпечено успішне виконання запиту до HERE Routing API (HTTP 200 OK).
    - Виправлено помилку "unknown field `origin`" шляхом переміщення параметрів `origin` та `destination` з тіла POST-запиту до URL запиту.
    - Виправлено помилку "unknown field `truck`" шляхом переміщення всіх параметрів транспортного засобу (`truck`) з тіла POST-запиту до URL запиту та зміни методу запиту на GET.
    - **Виправлено помилку "unknown field `truck`" (повторно) шляхом повернення параметрів `truck` до тіла POST-запиту, оскільки HERE Routing API v8 очікує їх там для `transportMode=truck`, тоді як `origin` та `destination` залишаються в URL.**

## Next Steps
- Перевірити структуру відповіді HERE API для платних доріг, оскільки поточний розрахунок повертає 0 євро. Можливо, потрібно буде запросити повний лог відповіді API для детального аналізу.
- Оновити `progress.md` з деталями виконаних завдань.

## Active Decisions and Considerations
- Використання GeoNames API для отримання координат міст є ключовим для будь-якого сервісу розрахунку маршрутів/доріг.
- HERE Routing API інтегровано для розрахунку вартості платних доріг.
- **Параметри `origin` та `destination` передаються в URL, а параметри `truck` передаються в тілі POST-запиту.**

## Important Patterns and Preferences
- Проект використовує Google Apps Script, що означає, що функції можуть бути глобально доступними або викликатися через меню/тригери в Google Sheets.
- Взаємодія з Google Sheets є ключовою.

## Learnings and Project Insights
- Проект є колекцією скриптів, що працюють разом для автоматизації логістичних та комерційних завдань.
- Залежність від Google Apps Script та зовнішніх API є центральною.
- **Критично важливо ретельно перевіряти документацію API щодо очікуваного формату запитів (GET/POST) та розташування параметрів (URL/тіло запиту), особливо для складних API, таких як HERE Routing API. Помилки в цьому можуть призвести до "unknown field" або "malformed request".**
