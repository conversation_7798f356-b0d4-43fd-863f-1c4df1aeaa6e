function removeDuplicateRowsByColumnD() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName("Населені пункти Європи");
  const column = 4; // стовпець D
  const lastRow = sheet.getLastRow();

  const values = sheet.getRange(2, column, lastRow - 1).getValues(); // значення з D2 до кінця
  const uniqueMap = new Map();
  const rowsToDelete = [];

  for (let i = 0; i < values.length; i++) {
    const cellValue = values[i][0];
    const rawText = (cellValue !== null && cellValue !== undefined) ? String(cellValue) : "";
    const cleaned = rawText.replace(/\s/g, '');

    if (cleaned === '') continue;

    if (uniqueMap.has(cleaned)) {
      rowsToDelete.push(i + 2); // +2 бо дані починаються з другого рядка
    } else {
      uniqueMap.set(cleaned, true);
    }
  }

  // Видалення рядків знизу вгору
  rowsToDelete.reverse().forEach(row => {
    sheet.deleteRow(row);
  });

  Logger.log(`Видалено ${rowsToDelete.length} дублікатів`);
}
