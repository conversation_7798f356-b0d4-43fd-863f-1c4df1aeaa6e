/**
 * Розраховує вартість платних доріг для фури між двома містами за допомогою HERE Routing API.
 *
 * @param {string} startCity Назва міста відправлення.
 * @param {string} endCity Назва міста прибуття.
 * @returns {number|null} Розрахована вартість платних доріг у євро або null у разі помилки.
 */
function calculateTollCost(startCity, endCity) {
  Logger.log(`Attempting to calculate toll cost from ${startCity} to ${endCity} using HERE Routing API.`);

  const apiKey = '79JmNq5MGllWma25lLzmr4-a_8LYPbjqxGFMjl_dHaY'; // Ваш API ключ для HERE Routing API

  // Отримання координат міст за допомогою GeoNames API
  const startCoords = getCityCoordinates(startCity);
  const endCoords = getCityCoordinates(endCity);

  if (!startCoords) {
    Logger.log(`Помилка: Не вдалося отримати координати для міста відправлення: ${startCity}`);
    return null;
  }
  if (!endCoords) {
    Logger.log(`Помилка: Не вдалося отримати координати для міста прибуття: ${endCity}`);
    return null;
  }

  Logger.log(`Координати ${startCity}: Lat ${startCoords.lat}, Lng ${startCoords.lng}`);
  Logger.log(`Координати ${endCity}: Lat ${endCoords.lat}, Lng ${endCoords.lng}`);

  // Параметри фури
  const vehicleSpecs = {
    length: 17, // метри
    width: 250, // сантиметри (2.5 метри)
    height: 4, // метри
    grossWeight: 40000, // кг (40 тонн)
    axleLoad: 11500, // кг (11.5 тонн на вісь)
    trailers: 1,
    emissionType: 'EURO_6', // HERE API може використовувати інший формат, наприклад 'EURO_6'
    totalAxles: 5
  };

  // Формування URL для HERE Routing API з усіма параметрами маршруту, транспортного засобу та API ключем
  // Видаляємо невідомі параметри та додаємо правильні параметри для отримання інформації про платні дороги
  const apiUrl = `https://router.hereapi.com/v8/routes?transportMode=truck&origin=${startCoords.lat},${startCoords.lng}&destination=${endCoords.lat},${endCoords.lng}&return=summary,tolls,polyline&spans=segmentRef&apiKey=${apiKey}&truck[length]=${vehicleSpecs.length}&truck[width]=${vehicleSpecs.width}&truck[height]=${vehicleSpecs.height}&truck[grossWeight]=${vehicleSpecs.grossWeight}`;

  Logger.log(`🔗 URL запиту до HERE API: ${apiUrl}`);

  // Для GET-запиту тіло запиту не потрібне
  const options = {
    method: 'get', // Змінено на GET
    muteHttpExceptions: true // Дозволяє отримати повну відповідь сервера при помилках
  };

  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    Logger.log(`Відповідь HERE API (Код: ${responseCode}): ${responseText}`);

    if (responseCode >= 200 && responseCode < 300) {
      const data = JSON.parse(responseText);

      // Детальне логування структури відповіді
      Logger.log(`🔍 Повна структура відповіді HERE API:`);
      Logger.log(JSON.stringify(data, null, 2));

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        Logger.log(`🔍 Структура маршруту:`);
        Logger.log(JSON.stringify(route, null, 2));

        let totalTollCost = 0;

        // Перевіряємо різні можливі місця розташування інформації про платні дороги
        Logger.log(`🔍 Перевіряємо route.sections...`);
        if (route.sections) {
          Logger.log(`✅ Знайдено ${route.sections.length} секцій маршруту`);
          route.sections.forEach((section, index) => {
            Logger.log(`🔍 Секція ${index} - тип: ${section.type}`);

            // Перевіряємо різні варіанти структури для платних доріг
            if (section.tolls) {
              Logger.log(`✅ Знайдено tolls в секції ${index}:`, JSON.stringify(section.tolls, null, 2));

              // Перевіряємо різні можливі поля з вартістю
              if (section.tolls.cost) {
                totalTollCost += parseFloat(section.tolls.cost);
                Logger.log(`💰 Додано ${section.tolls.cost} до загальної суми`);
              } else if (section.tolls.amount) {
                totalTollCost += parseFloat(section.tolls.amount);
                Logger.log(`💰 Додано ${section.tolls.amount} до загальної суми`);
              } else if (section.tolls.value) {
                totalTollCost += parseFloat(section.tolls.value);
                Logger.log(`💰 Додано ${section.tolls.value} до загальної суми`);
              } else if (Array.isArray(section.tolls)) {
                // Якщо tolls - це масив
                Logger.log(`📋 Знайдено масив платних доріг з ${section.tolls.length} елементів`);
                section.tolls.forEach((toll, tollIndex) => {
                  Logger.log(`  Платна дорога ${tollIndex}:`);
                  Logger.log(JSON.stringify(toll, null, 2));

                  // Перевіряємо всі можливі поля з вартістю
                  if (toll.cost !== undefined && toll.cost !== null) {
                    totalTollCost += parseFloat(toll.cost);
                    Logger.log(`💰 Додано ${toll.cost} (cost) до загальної суми`);
                  }
                  if (toll.amount !== undefined && toll.amount !== null) {
                    totalTollCost += parseFloat(toll.amount);
                    Logger.log(`💰 Додано ${toll.amount} (amount) до загальної суми`);
                  }
                  if (toll.value !== undefined && toll.value !== null) {
                    totalTollCost += parseFloat(toll.value);
                    Logger.log(`💰 Додано ${toll.value} (value) до загальної суми`);
                  }
                  if (toll.price !== undefined && toll.price !== null) {
                    totalTollCost += parseFloat(toll.price);
                    Logger.log(`💰 Додано ${toll.price} (price) до загальної суми`);
                  }
                  if (toll.fee !== undefined && toll.fee !== null) {
                    totalTollCost += parseFloat(toll.fee);
                    Logger.log(`💰 Додано ${toll.fee} (fee) до загальної суми`);
                  }

                  // Перевіряємо вкладені об'єкти
                  if (toll.summary && toll.summary.cost) {
                    totalTollCost += parseFloat(toll.summary.cost);
                    Logger.log(`💰 Додано ${toll.summary.cost} (summary.cost) до загальної суми`);
                  }
                });
              }
            }

            if (section.summary && section.summary.tolls) {
              Logger.log(`✅ Знайдено summary.tolls в секції ${index}:`, JSON.stringify(section.summary.tolls, null, 2));
              if (section.summary.tolls.cost) {
                totalTollCost += parseFloat(section.summary.tolls.cost);
                Logger.log(`💰 Додано ${section.summary.tolls.cost} до загальної суми`);
              } else if (section.summary.tolls.amount) {
                totalTollCost += parseFloat(section.summary.tolls.amount);
                Logger.log(`💰 Додано ${section.summary.tolls.amount} до загальної суми`);
              }
            }
          });
        }

        Logger.log(`🔍 Перевіряємо route.tolls...`);
        if (route.tolls) {
          Logger.log(`✅ Знайдено route.tolls:`, JSON.stringify(route.tolls, null, 2));
          if (route.tolls.cost) {
            totalTollCost += route.tolls.cost;
            Logger.log(`💰 Додано ${route.tolls.cost} до загальної суми`);
          }
        }

        Logger.log(`🔍 Перевіряємо route.summary...`);
        if (route.summary) {
          Logger.log(`✅ Знайдено route.summary:`, JSON.stringify(route.summary, null, 2));
          if (route.summary.tolls) {
            Logger.log(`✅ Знайдено route.summary.tolls:`, JSON.stringify(route.summary.tolls, null, 2));
            if (route.summary.tolls.cost) {
              totalTollCost += route.summary.tolls.cost;
              Logger.log(`💰 Додано ${route.summary.tolls.cost} до загальної суми`);
            }
          }
        }

        if (totalTollCost === 0) {
          Logger.log(`⚠️ Інформація про платні дороги не знайдена або вартість дорівнює 0. Можливо, маршрут не проходить через платні дороги.`);
        }

        Logger.log(`💰 Розрахована вартість платних доріг: ${totalTollCost} євро`);
        return totalTollCost;

      } else {
        Logger.log(`Не вдалося знайти маршрут або інформацію про платні дороги для ${startCity} до ${endCity}.`);
        return null;
      }
    } else {
      Logger.log(`❌ Помилка HTTP при виклику HERE Routing API. Код: ${responseCode}, Відповідь: ${responseText}`);
      return null;
    }
  } catch (e) {
    Logger.log(`❌ Помилка при виклику HERE Routing API: ${e.message}`);
    return null;
  }
}

/**
 * Тестова функція для перевірки calculateTollCost.
 */
function testCalculateTollCost() {
  const startCity = "Kyiv";
  const endCity = "Berlin";
  Logger.log(`🚛 Запуск тесту для розрахунку вартості платних доріг з ${startCity} до ${endCity}`);

  // Спочатку перевіримо координати окремо
  Logger.log(`🔍 Перевіряємо координати для ${startCity}...`);
  const startCoords = getCityCoordinates(startCity);
  Logger.log(`🔍 Перевіряємо координати для ${endCity}...`);
  const endCoords = getCityCoordinates(endCity);

  if (!startCoords || !endCoords) {
    Logger.log(`❌ Не вдалося отримати координати для одного з міст`);
    return;
  }

  Logger.log(`📍 ${startCity}: ${startCoords.lat}, ${startCoords.lng}`);
  Logger.log(`📍 ${endCity}: ${endCoords.lat}, ${endCoords.lng}`);

  const tollCost = calculateTollCost(startCity, endCity);
  if (tollCost !== null) {
    Logger.log(`✅ Вартість платних доріг для маршруту ${startCity} - ${endCity}: ${tollCost} євро`);
  } else {
    Logger.log(`❌ Не вдалося розрахувати вартість платних доріг для маршруту ${startCity} - ${endCity}. Дивіться лог для деталей.`);
  }
}

/**
 * Тестова функція для детального аналізу відповіді HERE API
 */
function debugHereApiResponse() {
  Logger.log(`🔧 Запуск детального аналізу відповіді HERE API...`);
  testCalculateTollCost();
}

/**
 * Тестова функція з конкретними координатами для перевірки HERE API
 */
function testWithKnownCoordinates() {
  Logger.log(`🧪 Тестуємо з відомими координатами Київ-Берлін...`);

  const apiKey = '79JmNq5MGllWma25lLzmr4-a_8LYPbjqxGFMjl_dHaY';

  // Відомі координати
  const kyivCoords = { lat: 50.4501, lng: 30.5234 }; // Київ
  const berlinCoords = { lat: 52.5200, lng: 13.4050 }; // Берлін

  Logger.log(`📍 Київ: ${kyivCoords.lat}, ${kyivCoords.lng}`);
  Logger.log(`📍 Берлін: ${berlinCoords.lat}, ${berlinCoords.lng}`);

  const vehicleSpecs = {
    length: 17,
    width: 250,
    height: 4,
    grossWeight: 40000
  };

  const apiUrl = `https://router.hereapi.com/v8/routes?transportMode=truck&origin=${kyivCoords.lat},${kyivCoords.lng}&destination=${berlinCoords.lat},${berlinCoords.lng}&return=summary,tolls,polyline&apiKey=${apiKey}&truck[length]=${vehicleSpecs.length}&truck[width]=${vehicleSpecs.width}&truck[height]=${vehicleSpecs.height}&truck[grossWeight]=${vehicleSpecs.grossWeight}`;

  Logger.log(`🔗 URL запиту: ${apiUrl}`);

  const options = {
    method: 'get',
    muteHttpExceptions: true
  };

  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    Logger.log(`📡 Відповідь HERE API (Код: ${responseCode})`);

    if (responseCode >= 200 && responseCode < 300) {
      const data = JSON.parse(responseText);
      Logger.log(`📊 Структура відповіді:`, JSON.stringify(data, null, 2));

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        Logger.log(`📏 Довжина маршруту: ${route.sections[0].summary.length} метрів`);
        Logger.log(`⏱️ Тривалість: ${route.sections[0].summary.duration} секунд`);

        // Перевіряємо наявність інформації про платні дороги
        if (route.sections[0].tolls) {
          Logger.log(`💰 Знайдено інформацію про платні дороги:`, JSON.stringify(route.sections[0].tolls, null, 2));
        } else {
          Logger.log(`⚠️ Інформація про платні дороги відсутня в секції`);
        }
      }
    } else {
      Logger.log(`❌ Помилка HTTP: ${responseCode} - ${responseText}`);
    }
  } catch (e) {
    Logger.log(`❌ Помилка запиту: ${e.message}`);
  }
}
