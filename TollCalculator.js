/**
 * Розраховує вартість платних доріг для фури між двома містами за допомогою HERE Routing API.
 *
 * @param {string} startCity Назва міста відправлення.
 * @param {string} endCity Назва міста прибуття.
 * @returns {number|null} Розрахована вартість платних доріг у євро або null у разі помилки.
 */
function calculateTollCost(startCity, endCity) {
  Logger.log(`Attempting to calculate toll cost from ${startCity} to ${endCity} using HERE Routing API.`);

  const apiKey = '79JmNq5MGllWma25lLzmr4-a_8LYPbjqxGFMjl_dHaY'; // Ваш API ключ для HERE Routing API

  // Отримання координат міст за допомогою GeoNames API
  const startCoords = getCityCoordinates(startCity);
  const endCoords = getCityCoordinates(endCity);

  if (!startCoords) {
    Logger.log(`Помилка: Не вдалося отримати координати для міста відправлення: ${startCity}`);
    return null;
  }
  if (!endCoords) {
    Logger.log(`Помилка: Не вдалося отримати координати для міста прибуття: ${endCity}`);
    return null;
  }

  Logger.log(`Координати ${startCity}: Lat ${startCoords.lat}, Lng ${startCoords.lng}`);
  Logger.log(`Координати ${endCity}: Lat ${endCoords.lat}, Lng ${endCoords.lng}`);

  // Параметри фури
  const vehicleSpecs = {
    length: 17, // метри
    width: 250, // сантиметри (2.5 метри)
    height: 4, // метри
    grossWeight: 40000, // кг (40 тонн)
    axleLoad: 11500, // кг (11.5 тонн на вісь)
    trailers: 1,
    emissionType: 'EURO_6', // HERE API може використовувати інший формат, наприклад 'EURO_6'
    totalAxles: 5
  };

  // Формування URL для HERE Routing API з усіма параметрами маршруту, транспортного засобу та API ключем
  const apiUrl = `https://router.hereapi.com/v8/routes?transportMode=truck&origin=${startCoords.lat},${startCoords.lng}&destination=${endCoords.lat},${endCoords.lng}&return=summary,tolls,polyline&spans=segmentRef&apiKey=${apiKey}&truck[length]=${vehicleSpecs.length}&truck[width]=${vehicleSpecs.width}&truck[height]=${vehicleSpecs.height}&truck[grossWeight]=${vehicleSpecs.grossWeight}&truck[axleLoad]=${vehicleSpecs.axleLoad}&truck[trailers]=${vehicleSpecs.trailers}&truck[emissionType]=${vehicleSpecs.emissionType}&truck[totalAxles]=${vehicleSpecs.totalAxles}`;

  // Для GET-запиту тіло запиту не потрібне
  const options = {
    method: 'get', // Змінено на GET
    muteHttpExceptions: true // Дозволяє отримати повну відповідь сервера при помилках
  };

  try {
    const response = UrlFetchApp.fetch(apiUrl, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    Logger.log(`Відповідь HERE API (Код: ${responseCode}): ${responseText}`);

    if (responseCode >= 200 && responseCode < 300) {
      const data = JSON.parse(responseText);

      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        let totalTollCost = 0;

        if (route.sections) {
          route.sections.forEach(section => {
            if (section.summary && section.summary.tolls && section.summary.tolls.amount) {
              totalTollCost += section.summary.tolls.amount;
            }
          });
        } else if (route.tolls && route.tolls.cost) {
          totalTollCost = route.tolls.cost;
        } else {
          Logger.log(`Інформація про платні дороги не знайдена у відповіді HERE API.`);
          return null;
        }

        Logger.log(`Розрахована вартість платних доріг: ${totalTollCost} євро`);
        return totalTollCost;

      } else {
        Logger.log(`Не вдалося знайти маршрут або інформацію про платні дороги для ${startCity} до ${endCity}.`);
        return null;
      }
    } else {
      Logger.log(`❌ Помилка HTTP при виклику HERE Routing API. Код: ${responseCode}, Відповідь: ${responseText}`);
      return null;
    }
  } catch (e) {
    Logger.log(`❌ Помилка при виклику HERE Routing API: ${e.message}`);
    return null;
  }
}

/**
 * Тестова функція для перевірки calculateTollCost.
 */
function testCalculateTollCost() {
  const startCity = "Kyiv";
  const endCity = "Berlin";
  Logger.log(`Запуск тесту для розрахунку вартості платних доріг з ${startCity} до ${endCity}`);
  const tollCost = calculateTollCost(startCity, endCity);
  if (tollCost !== null) {
    Logger.log(`Вартість платних доріг для маршруту ${startCity} - ${endCity}: ${tollCost} євро`);
  } else {
    Logger.log(`Не вдалося розрахувати вартість платних доріг для маршруту ${startCity} - ${endCity}. Дивіться лог для деталей.`);
  }
}
